'use client';

import React, { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  RefreshCw, 
  Clock, 
  Shield, 
  Lightbulb, 
  CheckCircle,
  XCircle,
  Info,
  Zap
} from 'lucide-react';

export interface EdgeCaseResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorType?: 'VALIDATION_ERROR' | 'SECURITY_ERROR' | 'PARSING_ERROR' | 'BUSINESS_LOGIC_ERROR' | 
             'DATA_CONSISTENCY_ERROR' | 'SYSTEM_ERROR' | 'AI_SERVICE_ERROR' | 'RESOURCE_ERROR' | 
             'TIMEOUT_ERROR' | 'CONCURRENCY_ERROR' | 'CIRCUIT_BREAKER_OPEN';
  fallbackData?: any;
  securityAlert?: boolean;
  retryable?: boolean;
  retryAfter?: number;
  retryCount?: number;
  isNewUser?: boolean;
  onboardingRecommendations?: string[];
  suggestedAlternatives?: any[];
  feasibilityAnalysis?: any;
  suggestedAdjustments?: any[];
  inconsistencies?: any[];
  suggestedCorrections?: any[];
  correctedData?: any;
  suggestedOptimizations?: string[];
  partialResults?: any;
  sanitizedInput?: any;
}

interface EdgeCaseResultHandlerProps {
  result: EdgeCaseResult;
  onRetry?: () => void;
  onUseAlternative?: (alternative: any) => void;
  onUseFallback?: () => void;
  showFallbackData?: boolean;
  className?: string;
}

const getErrorIcon = (errorType?: string) => {
  switch (errorType) {
    case 'SECURITY_ERROR':
      return <Shield className="h-4 w-4" />;
    case 'TIMEOUT_ERROR':
      return <Clock className="h-4 w-4" />;
    case 'CIRCUIT_BREAKER_OPEN':
      return <Zap className="h-4 w-4" />;
    case 'VALIDATION_ERROR':
      return <AlertTriangle className="h-4 w-4" />;
    default:
      return <XCircle className="h-4 w-4" />;
  }
};

const getErrorVariant = (errorType?: string) => {
  switch (errorType) {
    case 'SECURITY_ERROR':
      return 'destructive';
    case 'VALIDATION_ERROR':
      return 'default';
    case 'CIRCUIT_BREAKER_OPEN':
      return 'destructive';
    default:
      return 'destructive';
  }
};

export default function EdgeCaseResultHandler({
  result,
  onRetry,
  onUseAlternative,
  onUseFallback,
  showFallbackData = true,
  className = ''
}: EdgeCaseResultHandlerProps) {
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    if (!onRetry) return;
    
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  // Success case
  if (result.success) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>
            Operation completed successfully.
            {result.retryCount && result.retryCount > 0 && (
              <span className="ml-2 text-sm text-gray-600">
                (Succeeded after {result.retryCount} retries)
              </span>
            )}
          </AlertDescription>
        </Alert>

        {/* New User Onboarding */}
        {result.isNewUser && result.onboardingRecommendations && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Welcome! Getting Started Tips
              </CardTitle>
              <CardDescription>
                Since you're new, here are some recommendations to help you get started:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {result.onboardingRecommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // Error case
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Error Alert */}
      <Alert variant={getErrorVariant(result.errorType)}>
        {getErrorIcon(result.errorType)}
        <AlertTitle>
          {result.errorType?.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) || 'Error'}
          {result.securityAlert && (
            <Badge variant="destructive" className="ml-2">Security Alert</Badge>
          )}
        </AlertTitle>
        <AlertDescription>
          {result.error}
          {result.retryCount && result.retryCount > 0 && (
            <div className="mt-2 text-sm">
              Failed after {result.retryCount} retry attempts.
            </div>
          )}
        </AlertDescription>
      </Alert>

      {/* Retry Section */}
      {result.retryable && onRetry && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Retry Available</h4>
                <p className="text-sm text-gray-600">
                  This operation can be retried.
                  {result.retryAfter && (
                    <span className="ml-1">
                      Please wait {result.retryAfter} seconds before retrying.
                    </span>
                  )}
                </p>
              </div>
              <Button 
                onClick={handleRetry} 
                disabled={isRetrying}
                variant="outline"
                size="sm"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Suggested Alternatives */}
      {result.suggestedAlternatives && result.suggestedAlternatives.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Suggested Alternatives
            </CardTitle>
            <CardDescription>
              Try these alternatives instead:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              {result.suggestedAlternatives.map((alternative, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">{typeof alternative === 'string' ? alternative : alternative.name || alternative.title}</span>
                  {onUseAlternative && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onUseAlternative(alternative)}
                    >
                      Use This
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fallback Data */}
      {result.fallbackData && showFallbackData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              Fallback Data Available
            </CardTitle>
            <CardDescription>
              We have some cached or default data that might be helpful:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(result.fallbackData, null, 2)}
              </pre>
              {onUseFallback && (
                <Button size="sm" variant="outline" onClick={onUseFallback}>
                  Use Fallback Data
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Partial Results */}
      {result.partialResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              Partial Results
            </CardTitle>
            <CardDescription>
              Some data was processed before the error occurred:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {result.partialResults.processedItems !== undefined && (
                <div className="text-sm">
                  Processed: {result.partialResults.processedItems} / {result.partialResults.totalItems} items
                </div>
              )}
              {result.partialResults.timeElapsed && (
                <div className="text-sm">
                  Time elapsed: {result.partialResults.timeElapsed}ms
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Suggested Corrections */}
      {result.suggestedCorrections && result.suggestedCorrections.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Suggested Corrections
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-1">
              {result.suggestedCorrections.map((correction, index) => (
                <li key={index} className="text-sm flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-blue-500 mt-1 flex-shrink-0" />
                  {typeof correction === 'string' ? correction : correction.description || correction.message}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Circuit Breaker Status */}
      {result.errorType === 'CIRCUIT_BREAKER_OPEN' && (
        <Alert>
          <Zap className="h-4 w-4" />
          <AlertTitle>Service Temporarily Unavailable</AlertTitle>
          <AlertDescription>
            The service is experiencing issues and has been temporarily disabled to prevent further problems. 
            Please try again in a few minutes.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
