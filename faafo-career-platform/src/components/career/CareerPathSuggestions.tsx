import React, { useEffect, useState } from 'react';
import CareerPath<PERSON>ard from './CareerPathCard';
import { Skeleton } from '@/components/ui/skeleton'; // Assuming you have a Skeleton component

interface CareerPath {
  id: string;
  title: string;
  overview: string;
  pros: string[];
  cons: string[];
  actionable_steps: string[]; // Match DB schema if it's snake_case
}

interface CareerPathSuggestionsProps {
  assessmentId: string;
}

const CareerPathSuggestions: React.FC<CareerPathSuggestionsProps> = ({ assessmentId }) => {
  const [suggestions, setSuggestions] = useState<CareerPath[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!assessmentId) {
        setLoading(false);
        setError('Assessment ID is required.');
        return;
      }
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/career-suggestions?assessmentId=${assessmentId}`);
        const data = await response.json();

        if (!response.ok) {
          // Handle EdgeCaseHandler error responses
          if (data.fallbackData) {
            setSuggestions(data.fallbackData);
            setError('Using fallback career suggestions due to service issues');
          } else if (data.suggestedAlternatives) {
            setSuggestions(data.suggestedAlternatives);
            setError('Showing alternative career suggestions');
          } else {
            throw new Error(data.error || data.message || 'Failed to fetch career suggestions');
          }
        } else {
          setSuggestions(data);

          // Handle EdgeCaseHandler success metadata
          if (data.edgeCaseHandlerData?.isNewUser && data.edgeCaseHandlerData.onboardingRecommendations) {
            // Could show onboarding tips for new users
            console.info('New user detected, onboarding recommendations available:',
              data.edgeCaseHandlerData.onboardingRecommendations);
          }
        }
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message || 'An unexpected error occurred.');
        } else {
          setError('An unexpected error occurred.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSuggestions();
  }, [assessmentId]);

  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-center mb-6">Loading Career Suggestions...</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={`skeleton-${i}`} className="space-y-3 p-5 border rounded-md">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <div className="pt-4 space-y-2">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-3 w-full" />
                <Skeleton className="h-3 w-full" />
              </div>
             <div className="pt-4 space-y-2">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-3 w-full" />
                <Skeleton className="h-3 w-full" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500 text-center">Error: {error}</p>;
  }

  if (suggestions.length === 0) {
    return <p className="text-center">No career suggestions available at this time.</p>;
  }

  return (
    <div className="container mx-auto py-8">
      <h2 className="text-3xl font-bold text-center mb-8">Recommended Career Paths</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {suggestions.map((suggestion) => (
          <CareerPathCard
            key={suggestion.id}
            title={suggestion.title}
            overview={suggestion.overview}
            pros={suggestion.pros}
            cons={suggestion.cons}
            // Ensure prop name matches CareerPathCard's expected prop
            actionableSteps={suggestion.actionable_steps} 
            onClick={() => console.log(`Clicked on ${suggestion.title}`)} // Replace with actual navigation or modal
          />
        ))}
      </div>
    </div>
  );
};

export default CareerPathSuggestions; 