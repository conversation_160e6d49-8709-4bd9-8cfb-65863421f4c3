'use client';

import React from 'react';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface SkillData {
  skill: string;
  current: number;
  target: number;
  market: number;
  category: string;
}

interface SkillGapRadarChartProps {
  data: SkillData[];
  title?: string;
  description?: string;
  height?: number;
  showLegend?: boolean;
  maxValue?: number;
}

export default function SkillGapRadarChart({
  data,
  title = "Skill Gap Analysis",
  description = "Compare your current skills with target and market requirements",
  height = 400,
  showLegend = true,
  maxValue = 10,
}: SkillGapRadarChartProps) {
  const formatTooltip = (value: number, name: string) => {
    const labels = {
      current: 'Current Level',
      target: 'Target Level',
      market: 'Market Average',
    };
    return [`${value}/10`, labels[name as keyof typeof labels] || name];
  };

  const formatLabel = (value: string) => {
    // Truncate long skill names for better display
    return value.length > 15 ? `${value.substring(0, 12)}...` : value;
  };

  // Handle empty data gracefully
  const safeAverage = (values: number[]) => {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  };

  const averageCurrent = safeAverage(data.map(item => item.current));
  const averageTarget = safeAverage(data.map(item => item.target));
  const averageGap = safeAverage(data.map(item => Math.max(0, item.target - item.current)));

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {title}
        </CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer>
            <RadarChart data={data} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
              <PolarGrid
                className="stroke-gray-200 dark:stroke-gray-700"
                radialLines={true}
              />
              <PolarAngleAxis
                dataKey="skill"
                tick={{ fontSize: 12, fill: 'currentColor' }}
                tickFormatter={formatLabel}
                className="fill-gray-600 dark:fill-gray-400"
              />
              <PolarRadiusAxis
                angle={90}
                domain={[0, maxValue]}
                tick={{ fontSize: 10, fill: 'currentColor' }}
                className="fill-gray-500 dark:fill-gray-500"
              />

              <Radar
                name="Current Level"
                dataKey="current"
                stroke="#3b82f6"
                fill="#3b82f6"
                fillOpacity={0.1}
                strokeWidth={2}
              />

              <Radar
                name="Target Level"
                dataKey="target"
                stroke="#10b981"
                fill="#10b981"
                fillOpacity={0.1}
                strokeWidth={2}
                strokeDasharray="5 5"
              />

              <Radar
                name="Market Average"
                dataKey="market"
                stroke="#f59e0b"
                fill="#f59e0b"
                fillOpacity={0.05}
                strokeWidth={1}
                strokeDasharray="2 2"
              />

              <Tooltip
                formatter={formatTooltip}
                contentStyle={{
                  backgroundColor: 'var(--background)',
                  border: '1px solid var(--border)',
                  borderRadius: '6px',
                  color: 'var(--foreground)',
                }}
              />

              {showLegend && (
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  formatter={(value, entry) => (
                    <span style={{ color: entry.color }}>{value}</span>
                  )}
                />
              )}
            </RadarChart>
          </ResponsiveContainer>
        </div>

        {/* Skill Gap Summary */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Average Current
            </div>
            <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
              {averageCurrent.toFixed(1)}/10
            </div>
          </div>

          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-sm font-medium text-green-700 dark:text-green-300">
              Average Target
            </div>
            <div className="text-lg font-bold text-green-900 dark:text-green-100">
              {averageTarget.toFixed(1)}/10
            </div>
          </div>

          <div className="text-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
            <div className="text-sm font-medium text-amber-700 dark:text-amber-300">
              Gap to Close
            </div>
            <div className="text-lg font-bold text-amber-900 dark:text-amber-100">
              {averageGap.toFixed(1)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
