'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, AlertCircle, TrendingUp, Target } from 'lucide-react';

interface SkillProgressItem {
  id: string;
  name: string;
  category: string;
  currentLevel: number;
  targetLevel: number;
  progress: number; // 0-100
  status: 'not_started' | 'in_progress' | 'completed' | 'at_risk';
  estimatedTimeToComplete: number; // in weeks
  priority: 'low' | 'medium' | 'high' | 'critical';
  lastUpdated: string;
  milestones: {
    id: string;
    title: string;
    completed: boolean;
    dueDate?: string;
  }[];
}

interface SkillProgressTrackerProps {
  skills: SkillProgressItem[];
  title?: string;
  description?: string;
  showMilestones?: boolean;
  groupByCategory?: boolean;
}

export default function SkillProgressTracker({
  skills,
  title = "Skill Development Progress",
  description = "Track your progress towards your skill development goals",
  showMilestones = true,
  groupByCategory = false,
}: SkillProgressTrackerProps) {
  const getStatusIcon = (status: SkillProgressItem['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'at_risk':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Target className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: SkillProgressItem['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'at_risk':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: SkillProgressItem['priority']) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getProgressColor = (progress: number, status: SkillProgressItem['status']) => {
    if (status === 'completed') return 'bg-green-600';
    if (status === 'at_risk') return 'bg-red-600';
    if (progress >= 75) return 'bg-green-600';
    if (progress >= 50) return 'bg-blue-600';
    if (progress >= 25) return 'bg-yellow-600';
    return 'bg-gray-600';
  };

  const groupedSkills = groupByCategory
    ? skills.reduce((acc, skill) => {
        if (!acc[skill.category]) {
          acc[skill.category] = [];
        }
        acc[skill.category].push(skill);
        return acc;
      }, {} as Record<string, SkillProgressItem[]>)
    : { 'All Skills': skills };

  const overallProgress = skills.length > 0
    ? skills.reduce((sum, skill) => sum + skill.progress, 0) / skills.length
    : 0;

  const completedSkills = skills.filter(skill => skill.status === 'completed').length;
  const atRiskSkills = skills.filter(skill => skill.status === 'at_risk').length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          {title}
        </CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
        
        {/* Overall Progress Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Overall Progress
            </div>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {overallProgress.toFixed(1)}%
            </div>
          </div>
          
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-sm font-medium text-green-700 dark:text-green-300">
              Completed Skills
            </div>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">
              {completedSkills}/{skills.length}
            </div>
          </div>
          
          <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div className="text-sm font-medium text-red-700 dark:text-red-300">
              At Risk
            </div>
            <div className="text-2xl font-bold text-red-900 dark:text-red-100">
              {atRiskSkills}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {Object.entries(groupedSkills).map(([category, categorySkills]) => (
          <div key={category} className="space-y-4">
            {groupByCategory && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2">
                {category}
              </h3>
            )}
            
            <div className="space-y-4">
              {categorySkills.map((skill) => (
                <div key={skill.id} className="border rounded-lg p-4 space-y-3">
                  {/* Skill Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(skill.status)}
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {skill.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Level {skill.currentLevel} → {skill.targetLevel}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(skill.priority)}>
                        {skill.priority}
                      </Badge>
                      <Badge className={getStatusColor(skill.status)}>
                        {skill.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        Progress: {skill.progress}%
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        Est. {skill.estimatedTimeToComplete} weeks
                      </span>
                    </div>
                    <Progress 
                      value={skill.progress} 
                      className="h-2"
                    />
                  </div>
                  
                  {/* Milestones */}
                  {showMilestones && skill.milestones.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Milestones
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {skill.milestones.map((milestone) => (
                          <div
                            key={milestone.id}
                            className={`flex items-center gap-2 text-sm p-2 rounded ${
                              milestone.completed
                                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                                : 'bg-gray-50 dark:bg-gray-900/20 text-gray-600 dark:text-gray-400'
                            }`}
                          >
                            {milestone.completed ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <div className="h-3 w-3 border border-current rounded-full" />
                            )}
                            <span className="flex-1">{milestone.title}</span>
                            {milestone.dueDate && (
                              <span className="text-xs">
                                {new Date(milestone.dueDate).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Last Updated */}
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Last updated: {new Date(skill.lastUpdated).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
