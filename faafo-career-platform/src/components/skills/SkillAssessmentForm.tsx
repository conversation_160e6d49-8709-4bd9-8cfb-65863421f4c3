'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Loader2, Star } from 'lucide-react';
import { toast } from 'sonner';
import EdgeCaseResultHandler, { EdgeCaseResult } from '@/components/common/EdgeCaseResultHandler';

interface Skill {
  id: string;
  name: string;
  category: string;
  description?: string;
}

interface SkillAssessment {
  skillId: string;
  skillName: string;
  selfRating: number;
  confidenceLevel: number;
  assessmentType: 'SELF_ASSESSMENT' | 'PEER_VALIDATION' | 'CERTIFICATION' | 'PERFORMANCE_BASED' | 'AI_EVALUATED';
  notes?: string;
  yearsOfExperience?: number;
  lastUsed?: string;
}

interface SkillAssessmentFormProps {
  skills?: Skill[];
  onSubmit: (assessments: SkillAssessment[]) => Promise<void | EdgeCaseResult>;
  onSkillSearch?: (query: string) => Promise<Skill[]>;
  initialAssessments?: SkillAssessment[];
  mode?: 'single' | 'bulk';
  maxAssessments?: number;
  preserveStateOnSubmit?: boolean; // New prop to control form reset behavior
  onAssessmentsChange?: (assessments: SkillAssessment[]) => void; // Callback for external state management
}

export default function SkillAssessmentForm({
  onSubmit,
  onSkillSearch,
  initialAssessments = [],
  mode = 'single',
  maxAssessments = 20,
  preserveStateOnSubmit = false,
  onAssessmentsChange,
}: SkillAssessmentFormProps) {
  const [assessments, setAssessments] = useState<SkillAssessment[]>(
    initialAssessments.length > 0 ? initialAssessments : [createEmptyAssessment()]
  );
  const [edgeCaseResult, setEdgeCaseResult] = useState<EdgeCaseResult | null>(null);

  // Sync internal state with initialAssessments prop when it changes
  useEffect(() => {
    if (initialAssessments.length > 0) {
      setAssessments(initialAssessments);
    }
  }, [initialAssessments]);

  // Update assessments and notify parent if callback is provided
  const updateAssessments = (newAssessments: SkillAssessment[]) => {
    setAssessments(newAssessments);
    if (onAssessmentsChange) {
      onAssessmentsChange(newAssessments);
    }
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Skill[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  function createEmptyAssessment(): SkillAssessment {
    return {
      skillId: '',
      skillName: '',
      selfRating: 5,
      confidenceLevel: 5,
      assessmentType: 'SELF_ASSESSMENT',
      notes: '',
      yearsOfExperience: 0,
      lastUsed: '',
    };
  }

  // Search for skills
  useEffect(() => {
    const searchSkills = async () => {
      if (!searchQuery.trim() || !onSkillSearch) {
        setSearchResults([]);
        return;
      }

      setIsSearching(true);
      try {
        const results = await onSkillSearch(searchQuery);
        setSearchResults(results);
      } catch (error) {
        console.error('Error searching skills:', error);
        toast.error('Failed to search skills');
      } finally {
        setIsSearching(false);
      }
    };

    const debounceTimer = setTimeout(searchSkills, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, onSkillSearch]);

  const addAssessment = () => {
    if (assessments.length >= maxAssessments) {
      toast.error(`Maximum ${maxAssessments} assessments allowed`);
      return;
    }
    updateAssessments([...assessments, createEmptyAssessment()]);
  };

  const removeAssessment = (index: number) => {
    if (assessments.length <= 1) {
      toast.error('At least one assessment is required');
      return;
    }
    updateAssessments(assessments.filter((_, i) => i !== index));
  };

  const selectSkill = (index: number, skill: Skill) => {
    // Ensure the index is valid
    if (index < 0 || index >= assessments.length) {
      toast.error('Error selecting skill: Invalid assessment index');
      return;
    }

    updateAssessment(index, 'skillName', skill.name);
    // Only set skillId if it's a real UUID (not a fallback skill)
    if (skill.id && !skill.id.startsWith('common-')) {
      updateAssessment(index, 'skillId', skill.id);
    } else {
      // For fallback skills, generate a temporary ID or leave empty
      updateAssessment(index, 'skillId', `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);
    }
    setSearchQuery('');
    setSearchResults([]);

    // Show success feedback
    toast.success(`Selected skill: ${skill.name} for Assessment ${index + 1}`);
  };

  const updateAssessment = (index: number, field: keyof SkillAssessment, value: any) => {
    const updated = [...assessments];
    updated[index] = { ...updated[index], [field]: value };
    updateAssessments(updated);

    // Clear error for this field
    const errorKey = `${index}.${field}`;
    if (errors[errorKey]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }
  };



  const validateAssessments = (): boolean => {
    const newErrors: Record<string, string> = {};

    assessments.forEach((assessment, index) => {
      if (!assessment.skillName.trim()) {
        newErrors[`${index}.skillName`] = 'Skill name is required';
      }
      if (assessment.selfRating < 1 || assessment.selfRating > 10) {
        newErrors[`${index}.selfRating`] = 'Rating must be between 1 and 10';
      }
      if (assessment.confidenceLevel < 1 || assessment.confidenceLevel > 10) {
        newErrors[`${index}.confidenceLevel`] = 'Confidence must be between 1 and 10';
      }
      if (assessment.yearsOfExperience && assessment.yearsOfExperience < 0) {
        newErrors[`${index}.yearsOfExperience`] = 'Years of experience cannot be negative';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateAssessments()) {
      toast.error('Please fix the validation errors');
      return;
    }

    setIsSubmitting(true);
    setEdgeCaseResult(null); // Clear previous results

    try {
      const result = await onSubmit(assessments);

      // Check if the result is an EdgeCaseResult
      if (result && typeof result === 'object' && 'success' in result) {
        setEdgeCaseResult(result as EdgeCaseResult);

        if (result.success) {
          // Reset form based on mode and preserveStateOnSubmit prop
          if (mode === 'single' && !preserveStateOnSubmit) {
            updateAssessments([createEmptyAssessment()]);
            toast.success('Skill assessment submitted successfully!');
          } else {
            // For bulk mode or when preserveStateOnSubmit is true, keep the form data
            toast.success('Skill assessments saved! You can add more or proceed to analysis.');
          }
        } else {
          // Error case - EdgeCaseResultHandler will display the error
          toast.error(result.error || 'Failed to submit assessments');
        }
      } else {
        // Legacy response format
        if (mode === 'single' && !preserveStateOnSubmit) {
          updateAssessments([createEmptyAssessment()]);
          toast.success('Skill assessment submitted successfully!');
        } else {
          toast.success('Skill assessments saved! You can add more or proceed to analysis.');
        }
      }
    } catch (error) {
      console.error('Error submitting assessments:', error);
      setEdgeCaseResult({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to submit assessments',
        errorType: 'SYSTEM_ERROR',
        retryable: true
      });
      toast.error('Failed to submit assessments');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRatingLabel = (rating: number): string => {
    if (rating <= 2) return 'Beginner';
    if (rating <= 4) return 'Basic';
    if (rating <= 6) return 'Intermediate';
    if (rating <= 8) return 'Advanced';
    return 'Expert';
  };

  const getRatingColor = (rating: number): string => {
    if (rating <= 2) return 'bg-red-500';
    if (rating <= 4) return 'bg-orange-500';
    if (rating <= 6) return 'bg-yellow-500';
    if (rating <= 8) return 'bg-blue-500';
    return 'bg-green-500';
  };

  return (
    <div className="space-y-6">
      {/* EdgeCase Result Handler */}
      {edgeCaseResult && (
        <EdgeCaseResultHandler
          result={edgeCaseResult}
          onRetry={() => {
            setEdgeCaseResult(null);
            // Retry the last submission
            handleSubmit(new Event('submit') as any);
          }}
          onUseAlternative={(alternative) => {
            // Handle suggested alternatives
            if (typeof alternative === 'string') {
              // Add as a new skill assessment
              const newAssessment = createEmptyAssessment();
              newAssessment.skillName = alternative;
              updateAssessments([...assessments, newAssessment]);
            }
            setEdgeCaseResult(null);
          }}
          onUseFallback={() => {
            // Use fallback data if available
            setEdgeCaseResult(null);
            toast.info('Using fallback data');
          }}
        />
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Skill Assessment
          </CardTitle>
          <CardDescription>
            Assess your current skill levels to get personalized learning recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Skill Search */}
            {onSkillSearch && (
              <div className="space-y-2">
                <Label htmlFor="skill-search">Search Skills</Label>
                <div className="relative">
                  <Input
                    id="skill-search"
                    placeholder="Search for skills to assess..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  {isSearching && (
                    <Loader2 className="absolute right-3 top-3 h-4 w-4 animate-spin" />
                  )}
                </div>
                
                {searchResults.length > 0 && (
                  <div className="border rounded-md p-2 bg-white shadow-sm max-h-40 overflow-y-auto">
                    {searchResults.map((skill) => (
                      <button
                        key={skill.id}
                        type="button"
                        className="w-full text-left p-2 hover:bg-gray-100 rounded"
                        onClick={() => {
                          // Find the first empty assessment or use the last one
                          const emptyIndex = assessments.findIndex(a => !a.skillName || a.skillName.trim() === '');
                          const targetIndex = emptyIndex !== -1 ? emptyIndex : assessments.length - 1;
                          selectSkill(targetIndex, skill);
                        }}
                      >
                        <div className="font-medium">{skill.name}</div>
                        <div className="text-sm text-gray-500">{skill.category}</div>
                        {skill.description && (
                          <div className="text-xs text-gray-400 mt-1">{skill.description}</div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Assessment Forms */}
            {assessments.map((assessment, index) => (
              <Card key={index} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      Assessment {index + 1}
                    </CardTitle>
                    {mode === 'bulk' && assessments.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeAssessment(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Skill Name */}
                  <div className="space-y-2">
                    <Label htmlFor={`skill-name-${index}`}>Skill Name *</Label>
                    <Input
                      id={`skill-name-${index}`}
                      placeholder="e.g., JavaScript, React, Python"
                      value={assessment.skillName}
                      onChange={(e) => updateAssessment(index, 'skillName', e.target.value)}
                      className={errors[`${index}.skillName`] ? 'border-red-500' : ''}
                    />
                    {errors[`${index}.skillName`] && (
                      <p className="text-sm text-red-500">{errors[`${index}.skillName`]}</p>
                    )}
                  </div>

                  {/* Self Rating */}
                  <div className="space-y-3">
                    <Label>Self Rating: {assessment.selfRating}/10</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[assessment.selfRating]}
                        onValueChange={(value) => updateAssessment(index, 'selfRating', value[0])}
                        max={10}
                        min={1}
                        step={1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Beginner</span>
                        <span>Expert</span>
                      </div>
                      <Badge 
                        variant="secondary" 
                        className={`${getRatingColor(assessment.selfRating)} text-white`}
                      >
                        {getRatingLabel(assessment.selfRating)}
                      </Badge>
                    </div>
                  </div>

                  {/* Confidence Level */}
                  <div className="space-y-3">
                    <Label>Confidence Level: {assessment.confidenceLevel}/10</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[assessment.confidenceLevel]}
                        onValueChange={(value) => updateAssessment(index, 'confidenceLevel', value[0])}
                        max={10}
                        min={1}
                        step={1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Not Confident</span>
                        <span>Very Confident</span>
                      </div>
                    </div>
                  </div>

                  {/* Years of Experience */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`years-${index}`}>Years of Experience</Label>
                      <Input
                        id={`years-${index}`}
                        type="number"
                        min="0"
                        max="50"
                        placeholder="0"
                        value={assessment.yearsOfExperience || ''}
                        onChange={(e) => updateAssessment(index, 'yearsOfExperience', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor={`last-used-${index}`}>Last Used</Label>
                      <Input
                        id={`last-used-${index}`}
                        placeholder="e.g., Currently using"
                        value={assessment.lastUsed || ''}
                        onChange={(e) => updateAssessment(index, 'lastUsed', e.target.value)}
                      />
                    </div>
                  </div>

                  {/* Notes */}
                  <div className="space-y-2">
                    <Label htmlFor={`notes-${index}`}>Notes (Optional)</Label>
                    <Textarea
                      id={`notes-${index}`}
                      placeholder="Any additional context about your experience with this skill..."
                      value={assessment.notes || ''}
                      onChange={(e) => updateAssessment(index, 'notes', e.target.value)}
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Add Assessment Button */}
            {mode === 'bulk' && assessments.length < maxAssessments && (
              <Button
                type="button"
                variant="outline"
                onClick={addAssessment}
                className="w-full"
              >
                Add Another Skill Assessment
              </Button>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-32"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Assessment'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Progress Indicator */}
      {assessments.length > 1 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Assessment Progress</span>
              <span>{assessments.length} / {maxAssessments} skills</span>
            </div>
            <Progress value={(assessments.length / maxAssessments) * 100} className="h-2" />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
