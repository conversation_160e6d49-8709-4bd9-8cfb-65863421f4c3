/**
 * EdgeCaseHandler Database Integration Tests
 * 
 * Tests EdgeCaseHandler with real database connections, actual user data,
 * and production-like scenarios. This validates that edge case handling
 * works correctly with real data and database constraints.
 */

import { EdgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';
import { prisma } from '@/lib/prisma';
import { TestDatabase } from '../utils/testHelpers';

describe('EdgeCaseHandler Database Integration', () => {
  let edgeCaseHandlerService: EdgeCaseHandlerService;
  let testDb: TestDatabase;
  let testUser: any;
  let testSkills: any[];
  let testCareerPath: any;

  beforeAll(async () => {
    // Initialize EdgeCaseHandler service
    edgeCaseHandlerService = EdgeCaseHandlerService.getInstance();

    // Initialize test database
    testDb = new TestDatabase();

    // Create test user
    testUser = await testDb.createTestUser({
      email: '<EMAIL>',
      name: 'EdgeCase Test User',
      password: 'testpassword123'
    });

    // Mock test skills data
    testSkills = [
      {
        id: 'skill-js-123',
        name: 'JavaScript',
        category: 'PROGRAMMING',
        description: 'JavaScript programming language'
      },
      {
        id: 'skill-react-456',
        name: 'React',
        category: 'FRONTEND',
        description: 'React framework'
      },
      {
        id: 'skill-node-789',
        name: 'Node.js',
        category: 'BACKEND',
        description: 'Node.js runtime'
      }
    ];

    // Mock test career path
    testCareerPath = {
      id: 'career-fullstack-123',
      name: 'Full Stack Developer',
      description: 'Full stack web development career path',
      skillIds: testSkills.map(skill => skill.id)
    };
  });

  afterAll(async () => {
    await testDb.cleanup();
  });

  beforeEach(async () => {
    // Reset mocks for clean test state
    jest.clearAllMocks();
  });

  describe('Real Database Skill Assessment Creation', () => {
    it('should create skill assessment with database integration', async () => {
      // Arrange
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [testSkills[0].id],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: testCareerPath.id
      };

      // Mock successful skill lookup
      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.skill.findMany.mockResolvedValue([testSkills[0]]);

      // Mock successful assessment creation
      const mockAssessment = {
        id: 'assessment-123',
        userId: testUser.id,
        skillId: testSkills[0].id,
        selfRating: 7,
        confidenceLevel: 8,
        assessmentType: 'SELF_ASSESSMENT',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      mockPrisma.skillAssessment.create.mockResolvedValue(mockAssessment);

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.sanitizedInput).toBeDefined();
      expect(result.isNewUser).toBe(true); // First assessment for this user

      // Verify the service called the database methods
      expect(mockPrisma.skill.findMany).toHaveBeenCalledWith({
        where: { id: { in: [testSkills[0].id] } }
      });
    });

    it('should handle invalid skill IDs with database validation', async () => {
      // Arrange
      const invalidSkillId = 'invalid-skill-id-12345';
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [invalidSkillId],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: testCareerPath.id
      };

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.error).toContain('Skills not found');
      expect(result.suggestedAlternatives).toBeDefined();
      expect(result.fallbackData).toBeDefined();

      // Verify no assessment was created
      const assessmentCount = await prisma.skillAssessment.count({
        where: { userId: testUser.id }
      });
      expect(assessmentCount).toBe(0);
    });

    it('should handle database connection failures gracefully', async () => {
      // Arrange - Temporarily disconnect database
      await prisma.$disconnect();
      
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [testSkills[0].id],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: testCareerPath.id
      };

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('SYSTEM_ERROR');
      expect(result.fallbackData).toBeDefined();
      expect(result.retryable).toBe(true);

      // Reconnect database for other tests
      await prisma.$connect();
    });

    it('should handle concurrent assessment creation', async () => {
      // Arrange
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [testSkills[0].id],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: testCareerPath.id
      };

      // Act - Create multiple assessments concurrently
      const promises = Array(5).fill(null).map(() => 
        edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest)
      );
      const results = await Promise.all(promises);

      // Assert - At least one should succeed
      const successfulResults = results.filter(r => r.success);
      expect(successfulResults.length).toBeGreaterThan(0);

      // Verify database consistency
      const assessmentCount = await prisma.skillAssessment.count({
        where: { userId: testUser.id }
      });
      expect(assessmentCount).toBeGreaterThan(0);
    });
  });

  describe('Real Database User Data Retrieval', () => {
    beforeEach(async () => {
      // Create some test assessment data
      await testDb.createTestSkillAssessment(testUser.id, {
        skillId: testSkills[0].id,
        selfRating: 7,
        confidenceLevel: 8,
        assessmentType: 'SELF_ASSESSMENT'
      });
    });

    it('should retrieve user skill assessments with market data', async () => {
      // Arrange
      const retrievalRequest = {
        userId: testUser.id,
        dataType: 'skill_assessments',
        includeMarketData: true
      };

      // Act
      const result = await edgeCaseHandlerService.handleUserDataRetrieval(retrievalRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);

      // Verify data structure
      const assessment = result.data[0];
      expect(assessment.skillId).toBe(testSkills[0].id);
      expect(assessment.skill).toBeDefined();
    });

    it('should handle user with no assessment data', async () => {
      // Arrange - Create new user with no assessments
      const newUser = await testDb.createTestUser({
        email: '<EMAIL>',
        name: 'No Data User',
        password: 'testpassword123'
      });

      const retrievalRequest = {
        userId: newUser.id,
        dataType: 'skill_assessments',
        includeMarketData: true
      };

      // Act
      const result = await edgeCaseHandlerService.handleUserDataRetrieval(retrievalRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBe(0);
      expect(result.isNewUser).toBe(true);
      expect(result.onboardingRecommendations).toBeDefined();
    });
  });

  describe('Real Database AI Skills Analysis', () => {
    it('should handle AI analysis with real user data', async () => {
      // Arrange - Create user with assessment history
      await testDb.createTestSkillAssessment(testUser.id, {
        skillId: testSkills[0].id,
        selfRating: 6,
        confidenceLevel: 7,
        assessmentType: 'SELF_ASSESSMENT'
      });

      const analysisRequest = {
        userId: testUser.id,
        currentSkills: ['JavaScript', 'HTML', 'CSS'],
        targetCareerPath: 'Full Stack Developer',
        experienceLevel: 'mid',
        timeframe: '6_months',
        focusAreas: ['backend', 'databases'],
        includeMarketData: true,
        careerPathData: {
          requiredSkills: ['JavaScript', 'React', 'Node.js', 'MongoDB'],
          learningResources: []
        }
      };

      // Act
      const result = await edgeCaseHandlerService.handleAISkillsAnalysis(analysisRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.sanitizedInput).toBeDefined();
      
      // Verify AI analysis structure
      expect(result.data.skillGaps).toBeDefined();
      expect(result.data.learningPlan).toBeDefined();
      expect(result.data.careerReadiness).toBeDefined();
    });

    it('should provide fallback when AI service fails', async () => {
      // Arrange - Request with invalid data to trigger AI failure
      const analysisRequest = {
        userId: testUser.id,
        currentSkills: [], // Empty skills to trigger failure
        targetCareerPath: '',
        experienceLevel: 'invalid' as any,
        timeframe: '6_months',
        focusAreas: [],
        includeMarketData: true,
        careerPathData: null
      };

      // Act
      const result = await edgeCaseHandlerService.handleAISkillsAnalysis(analysisRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.fallbackData).toBeDefined();
      expect(result.suggestedAlternatives).toBeDefined();
      
      // Verify fallback data structure
      expect(result.fallbackData.skillGaps).toBeDefined();
      expect(result.fallbackData.learningPlan).toBeDefined();
      expect(result.fallbackData.careerReadiness).toBeDefined();
    });
  });

  describe('Database Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      // Arrange - Create multiple skills and assessments
      const manySkills = await Promise.all(
        Array(20).fill(null).map((_, index) => 
          testDb.createTestSkill({
            name: `Skill ${index}`,
            category: 'PROGRAMMING',
            description: `Test skill ${index}`
          })
        )
      );

      // Create assessments for all skills
      await Promise.all(
        manySkills.map(skill => 
          testDb.createTestSkillAssessment(testUser.id, {
            skillId: skill.id,
            selfRating: Math.floor(Math.random() * 10) + 1,
            confidenceLevel: Math.floor(Math.random() * 10) + 1,
            assessmentType: 'SELF_ASSESSMENT'
          })
        )
      );

      const startTime = Date.now();

      // Act
      const result = await edgeCaseHandlerService.handleUserDataRetrieval({
        userId: testUser.id,
        dataType: 'skill_assessments',
        includeMarketData: true
      });

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.length).toBe(20);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle database transaction rollbacks', async () => {
      // Arrange - Create a scenario that will cause transaction rollback
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [testSkills[0].id, 'invalid-skill-id'], // Mix valid and invalid
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: testCareerPath.id
      };

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(false);
      
      // Verify no partial data was created (transaction rolled back)
      const assessmentCount = await prisma.skillAssessment.count({
        where: { userId: testUser.id }
      });
      expect(assessmentCount).toBe(0);
    });
  });
});
