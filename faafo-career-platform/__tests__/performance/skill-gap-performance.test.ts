import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { performance } from 'perf_hooks';

// Mock environment variables for testing
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-performance-testing';
process.env.NODE_ENV = 'test';

import { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';
import { geminiService } from '@/lib/services/geminiService';
import { cacheService } from '@/lib/services/cacheService';

// Mock external dependencies for performance testing
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn().mockImplementation((params) => {
        // Return user for any test user ID
        if (params.where.id.includes('test') || params.where.id.includes('perf')) {
          return Promise.resolve({
            id: params.where.id,
            email: `${params.where.id}@example.com`,
            name: `Test User ${params.where.id}`,
            createdAt: new Date()
          });
        }
        return Promise.resolve(null);
      }),
    },
    skill: {
      findMany: jest.fn().mockImplementation((params) => {
        const skillMap = {
          'javascript': { id: 'javascript', name: 'JavaScript', category: 'Programming' },
          'react': { id: 'react', name: 'React', category: 'Frontend' },
          'nodejs': { id: 'nodejs', name: 'Node.js', category: 'Backend' },
          'python': { id: 'python', name: 'Python', category: 'Programming' },
          'typescript': { id: 'typescript', name: 'TypeScript', category: 'Programming' }
        };

        if (params.where?.id?.in) {
          return Promise.resolve(
            params.where.id.in.map((id: string) => skillMap[id as keyof typeof skillMap]).filter(Boolean)
          );
        }
        return Promise.resolve(Object.values(skillMap));
      }),
      findFirst: jest.fn().mockImplementation((params) => {
        const skillName = params.where?.name?.equals?.toLowerCase() || params.where?.name?.equals;
        const skillMap = {
          'javascript': { id: 'javascript', name: 'JavaScript', category: 'Programming', marketData: [] },
          'react': { id: 'react', name: 'React', category: 'Frontend', marketData: [] },
          'nodejs': { id: 'nodejs', name: 'Node.js', category: 'Backend', marketData: [] },
          'python': { id: 'python', name: 'Python', category: 'Programming', marketData: [] },
          'typescript': { id: 'typescript', name: 'TypeScript', category: 'Programming', marketData: [] }
        };
        return Promise.resolve(skillMap[skillName as keyof typeof skillMap] || null);
      }),
    },
    skillAssessment: {
      create: jest.fn().mockResolvedValue({ id: 'test-assessment', userId: 'test-user' }),
    },
    learningPath: {
      create: jest.fn().mockResolvedValue({ id: 'test-path', userId: 'test-user' }),
    },
    skillMarketData: {
      create: jest.fn().mockResolvedValue({ id: 'test-market-data', skillId: 'javascript' }),
      findFirst: jest.fn().mockResolvedValue({
        demand: 80,
        averageSalary: 95000,
        growth: 15,
        dataDate: new Date(),
        metadata: {}
      }),
    },
    careerPath: {
      findFirst: jest.fn().mockResolvedValue({
        id: 'test-career-path',
        title: 'Full Stack Developer',
        skills: []
      }),
    },
    $transaction: jest.fn().mockImplementation((callback) => callback({
      skillAssessment: {
        create: jest.fn().mockResolvedValue({ id: 'test-assessment', userId: 'test-user' }),
      }
    })),
  },
}));

// Mock Gemini service for performance testing
jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeSkillsGap: jest.fn().mockImplementation(async () => {
      // Simulate AI processing time
      await new Promise(resolve => setTimeout(resolve, 100));
      return {
        success: true,
        data: {
          skillGaps: [],
          learningRecommendations: [],
          marketInsights: {},
        },
        cached: Math.random() > 0.5, // Simulate cache hits/misses
      };
    }),
  },
}));

// Performance test configuration
const PERFORMANCE_THRESHOLDS = {
  skillAssessment: {
    maxResponseTime: 2000, // 2 seconds
    maxMemoryUsage: 50 * 1024 * 1024, // 50MB
    maxCacheSize: 100 * 1024 * 1024, // 100MB
  },
  skillsAnalysis: {
    maxResponseTime: 5000, // 5 seconds
    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    maxCacheSize: 200 * 1024 * 1024, // 200MB
  },
  learningPath: {
    maxResponseTime: 3000, // 3 seconds
    maxMemoryUsage: 75 * 1024 * 1024, // 75MB
    maxCacheSize: 150 * 1024 * 1024, // 150MB
  },
  marketData: {
    maxResponseTime: 1000, // 1 second
    maxMemoryUsage: 25 * 1024 * 1024, // 25MB
    maxCacheSize: 50 * 1024 * 1024, // 50MB
  }
};

interface PerformanceMetrics {
  responseTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  errorRate: number;
  throughput: number;
}

class PerformanceTester {
  private initialMemory: number = 0;
  private startTime: number = 0;
  private requestCount: number = 0;
  private errorCount: number = 0;
  private cacheHits: number = 0;
  private cacheMisses: number = 0;

  startTest(): void {
    this.initialMemory = process.memoryUsage().heapUsed;
    this.startTime = performance.now();
    this.requestCount = 0;
    this.errorCount = 0;
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }

  recordRequest(success: boolean, cacheHit: boolean = false): void {
    this.requestCount++;
    if (!success) this.errorCount++;
    if (cacheHit) this.cacheHits++;
    else this.cacheMisses++;
  }

  getMetrics(): PerformanceMetrics {
    const endTime = performance.now();
    const currentMemory = process.memoryUsage().heapUsed;
    const duration = endTime - this.startTime;

    return {
      responseTime: duration,
      memoryUsage: currentMemory - this.initialMemory,
      cacheHitRate: this.requestCount > 0 ? this.cacheHits / this.requestCount : 0,
      errorRate: this.requestCount > 0 ? this.errorCount / this.requestCount : 0,
      throughput: this.requestCount / (duration / 1000) // requests per second
    };
  }
}

describe('Skill Gap Analyzer Performance Tests', () => {
  let performanceTester: PerformanceTester;

  beforeEach(() => {
    performanceTester = new PerformanceTester();
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any test data
    jest.restoreAllMocks();
  });

  describe('Skill Assessment Performance', () => {
    it('should complete skill assessment within performance thresholds', async () => {
      performanceTester.startTest();

      const testData = {
        userId: 'perf-test-user',
        skillIds: ['javascript', 'react', 'nodejs'],
        assessmentType: 'comprehensive'
      };

      try {
        const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(testData);
        performanceTester.recordRequest(result.success);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      const metrics = performanceTester.getMetrics();

      expect(metrics.responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.skillAssessment.maxResponseTime);
      expect(metrics.memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.skillAssessment.maxMemoryUsage);
      expect(metrics.errorRate).toBeLessThan(0.05); // Less than 5% error rate
    });

    it('should handle concurrent skill assessments efficiently', async () => {
      performanceTester.startTest();

      const concurrentRequests = 10;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        const testData = {
          userId: `perf-test-user-${i}`,
          skillIds: ['javascript', 'react'],
          assessmentType: 'quick'
        };

        promises.push(
          edgeCaseHandlerService.createSkillAssessmentWithDatabase(testData)
            .then(result => performanceTester.recordRequest(result.success))
            .catch(() => performanceTester.recordRequest(false))
        );
      }

      await Promise.all(promises);
      const metrics = performanceTester.getMetrics();

      expect(metrics.throughput).toBeGreaterThan(2); // At least 2 requests per second
      expect(metrics.errorRate).toBeLessThan(0.1); // Less than 10% error rate under load
      expect(metrics.memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.skillAssessment.maxMemoryUsage * 2);
    });
  });

  describe('Skills Analysis Performance', () => {
    it('should complete skills analysis within performance thresholds', async () => {
      performanceTester.startTest();

      const testData = {
        currentSkills: ['JavaScript', 'React', 'Node.js'],
        targetCareerPath: 'Full Stack Developer',
        experienceLevel: 'mid',
        timeframe: '6_months'
      };

      try {
        const result = await geminiService.analyzeSkillsGap(
          testData.currentSkills,
          testData.targetCareerPath,
          testData.experienceLevel,
          'perf-test-user'
        );
        performanceTester.recordRequest(result.success, result.cached);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      const metrics = performanceTester.getMetrics();

      expect(metrics.responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.skillsAnalysis.maxResponseTime);
      expect(metrics.memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.skillsAnalysis.maxMemoryUsage);
    });

    it('should leverage caching for repeated analysis requests', async () => {
      performanceTester.startTest();

      const testData = {
        currentSkills: ['JavaScript', 'React'],
        targetCareerPath: 'Frontend Developer',
        experienceLevel: 'entry',
        timeframe: '3_months'
      };

      // First request (cache miss)
      try {
        const result1 = await geminiService.analyzeSkillsGap(
          testData.currentSkills,
          testData.targetCareerPath,
          testData.experienceLevel,
          'cache-test-user'
        );
        performanceTester.recordRequest(result1.success, false);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      // Second request (should be cached)
      try {
        const result2 = await geminiService.analyzeSkillsGap(
          testData.currentSkills,
          testData.targetCareerPath,
          testData.experienceLevel,
          'cache-test-user'
        );
        performanceTester.recordRequest(result2.success, result2.cached);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      const metrics = performanceTester.getMetrics();

      expect(metrics.cacheHitRate).toBeGreaterThan(0.4); // At least 40% cache hit rate
    });
  });

  describe('Learning Path Performance', () => {
    it('should generate learning paths within performance thresholds', async () => {
      performanceTester.startTest();

      const testData = {
        userId: 'perf-test-user',
        targetRole: 'Full Stack Developer',
        currentSkills: [
          { skill: 'JavaScript', level: 6 },
          { skill: 'React', level: 5 },
          { skill: 'Node.js', level: 4 }
        ],
        timeframe: 6,
        budget: 1000,
        availability: 10
      };

      try {
        const result = await edgeCaseHandlerService.generateLearningPathWithDatabase(testData);
        performanceTester.recordRequest(result.success);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      const metrics = performanceTester.getMetrics();

      expect(metrics.responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.learningPath.maxResponseTime);
      expect(metrics.memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.learningPath.maxMemoryUsage);
    });
  });

  describe('Market Data Performance', () => {
    it('should retrieve market data within performance thresholds', async () => {
      performanceTester.startTest();

      const testData = {
        skill: 'JavaScript',
        location: 'San Francisco'
      };

      try {
        const result = await edgeCaseHandlerService.getMarketDataWithDatabase(testData);
        performanceTester.recordRequest(result.success);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      const metrics = performanceTester.getMetrics();

      expect(metrics.responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.marketData.maxResponseTime);
      expect(metrics.memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.marketData.maxMemoryUsage);
    });

    it('should handle batch market data requests efficiently', async () => {
      performanceTester.startTest();

      const skills = ['JavaScript', 'Python', 'React', 'Node.js', 'TypeScript'];
      const promises = skills.map(skill => 
        edgeCaseHandlerService.getMarketDataWithDatabase({ skill })
          .then(result => performanceTester.recordRequest(result.success))
          .catch(() => performanceTester.recordRequest(false))
      );

      await Promise.all(promises);
      const metrics = performanceTester.getMetrics();

      expect(metrics.throughput).toBeGreaterThan(3); // At least 3 requests per second
      expect(metrics.errorRate).toBeLessThan(0.1); // Less than 10% error rate
    });
  });

  describe('Memory Management', () => {
    it('should not have memory leaks during extended operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple operations
      for (let i = 0; i < 20; i++) {
        await edgeCaseHandlerService.getMarketDataWithDatabase({
          skill: `test-skill-${i}`
        });
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 20MB for 20 operations)
      expect(memoryIncrease).toBeLessThan(20 * 1024 * 1024);
    });
  });

  describe('Database Query Performance', () => {
    it('should execute database queries efficiently', async () => {
      // This test would require actual database connection
      // For now, we'll test the EdgeCaseHandler's database integration methods
      
      performanceTester.startTest();

      const testData = {
        userId: 'db-perf-test-user',
        skillIds: ['javascript'],
        assessmentType: 'quick'
      };

      try {
        const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(testData);
        performanceTester.recordRequest(result.success);
      } catch (error) {
        performanceTester.recordRequest(false);
      }

      const metrics = performanceTester.getMetrics();

      // Database operations should be fast
      expect(metrics.responseTime).toBeLessThan(1000); // 1 second for DB operations
    });
  });
});
