import { NextRequest } from 'next/server';
import { POST } from '../../../src/app/api/skills/assessment/route';

// Mock NextAuth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn()
}));

// Mock EdgeCaseHandlerService
jest.mock('@/lib/skills/EdgeCaseHandlerService', () => ({
  edgeCaseHandlerService: {
    createSkillAssessmentWithDatabase: jest.fn(),
  }
}));

// Mock Gemini Service
jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeSkillsGap: jest.fn(),
  }
}));

// Mock Performance Monitor
jest.mock('@/lib/performance/skill-gap-performance', () => ({
  skillGapPerformanceMonitor: {
    monitorSkillAnalysis: jest.fn(),
  }
}));

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    skillAssessment: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock middleware
jest.mock('@/lib/csrf', () => ({
  withCSRFProtection: jest.fn((handler) => handler),
}));

jest.mock('@/lib/rateLimit', () => ({
  withRateLimit: jest.fn((request, options, handler) => handler()),
}));

jest.mock('@/lib/errorHandler', () => ({
  withErrorHandler: jest.fn((handler) => handler),
}));

const { getServerSession } = require('next-auth/next');
const { edgeCaseHandlerService } = require('@/lib/skills/EdgeCaseHandlerService');

describe('Skills Assessment API - EdgeCaseHandler Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock authenticated session
    getServerSession.mockResolvedValue({
      user: { id: 'test-user-123', email: '<EMAIL>' },
      expires: '2024-12-31',
    });
  });

  describe('POST /api/skills/assessment - EdgeCaseHandler Integration', () => {
    it('should use EdgeCaseHandler for skill assessment creation', async () => {
      // Arrange
      const mockEdgeCaseResult = {
        success: true,
        data: {
          id: 'assessment-123',
          databaseId: 'db-assessment-123',
        },
        sanitizedInput: {
          userId: 'test-user-123',
          skillId: 'javascript',
          assessmentType: 'comprehensive',
        },
        isNewUser: false,
        retryCount: 0,
      };

      edgeCaseHandlerService.createSkillAssessmentWithDatabase.mockResolvedValue(mockEdgeCaseResult);

      const request = new NextRequest('http://localhost:3000/api/skills/assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          skillId: 'javascript',
          selfRating: 7,
          confidenceLevel: 8,
          assessmentType: 'comprehensive',
        }),
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.edgeCaseHandlerData).toBeDefined();
      expect(edgeCaseHandlerService.createSkillAssessmentWithDatabase).toHaveBeenCalled();
    });

    it('should handle authentication failure', async () => {
      // Arrange
      getServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/skills/assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          skillId: 'javascript',
          selfRating: 7,
          confidenceLevel: 8,
        }),
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });
  });
});