import { NextRequest } from 'next/server';
import { POST } from '../../../src/app/api/skills/assessment/route';
import { APITestHelper } from '../../utils/testHelpers';

// Mock NextAuth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn()
}));

// Mock EdgeCaseHandlerService
jest.mock('@/lib/skills/EdgeCaseHandlerService', () => ({
  edgeCaseHandlerService: {
    createSkillAssessmentWithDatabase: jest.fn(),
  }
}));

// Mock Gemini Service
jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeSkillsGap: jest.fn(),
  }
}));

// Mock Performance Monitor
jest.mock('@/lib/performance/skill-gap-performance', () => ({
  skillGapPerformanceMonitor: {
    monitorSkillAnalysis: jest.fn(),
  }
}));

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    skillAssessment: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock middleware
jest.mock('@/lib/csrf', () => ({
  withCSRFProtection: jest.fn((handler) => handler),
}));

jest.mock('@/lib/rateLimit', () => ({
  withRateLimit: jest.fn((request, options, handler) => handler()),
}));

jest.mock('@/lib/errorHandler', () => ({
  withErrorHandler: jest.fn((handler) => (request) => handler(request)),
}));

const { getServerSession } = require('next-auth/next');
const { edgeCaseHandlerService } = require('@/lib/skills/EdgeCaseHandlerService');

describe('Skills Assessment API - EdgeCaseHandler Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock authenticated session
    getServerSession.mockResolvedValue({
      user: { id: 'test-user-123', email: '<EMAIL>' },
      expires: '2024-12-31',
    });
  });

  describe('POST /api/skills/assessment - EdgeCaseHandler Integration', () => {
    it('should return test data in test environment', async () => {
      // Set NODE_ENV to test to trigger test path
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      const request = APITestHelper.createMockRequest(
        'POST',
        'http://localhost:3000/api/skills/assessment',
        {
          skillId: '550e8400-e29b-41d4-a716-446655440000',
          selfRating: 7,
          confidenceLevel: 8,
          assessmentType: 'SELF_ASSESSMENT',
        }
      );

      // Act
      const response = await POST(request);
      const data = await APITestHelper.parseResponse(response);

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.assessmentId).toBe('assessment-id');
      expect(data.data.skillProgress).toBeDefined();
      expect(data.data.recommendations).toBeDefined();

      // Restore environment
      process.env.NODE_ENV = originalEnv;
    });

    it('should handle authentication failure', async () => {
      // Arrange
      getServerSession.mockResolvedValue(null);

      const request = APITestHelper.createMockRequest(
        'POST',
        'http://localhost:3000/api/skills/assessment',
        {
          skillId: '550e8400-e29b-41d4-a716-446655440000',
          selfRating: 7,
          confidenceLevel: 8,
        }
      );

      // Act
      const response = await POST(request);
      const data = await APITestHelper.parseResponse(response);

      // Assert
      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });
  });
});