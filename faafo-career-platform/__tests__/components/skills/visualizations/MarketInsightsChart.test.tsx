import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import MarketInsightsChart from '@/components/skills/visualizations/MarketInsightsChart';

// Mock recharts components
jest.mock('recharts', () => ({
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: ({ dataKey, fill }: any) => <div data-testid="bar" data-key={dataKey} data-fill={fill} />,
  XAxis: ({ dataKey }: any) => <div data-testid="x-axis" data-key={dataKey} />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  Legend: () => <div data-testid="legend" />,
}));

const mockMarketData = [
  {
    skill: 'JavaScript',
    demand: 85,
    supply: 70,
    averageSalary: 95000,
    growth: 12.5,
    difficulty: 6,
    timeToLearn: 8,
    category: 'Programming',
  },
  {
    skill: 'React',
    demand: 80,
    supply: 65,
    averageSalary: 90000,
    growth: 15.2,
    difficulty: 7,
    timeToLearn: 10,
    category: 'Frontend',
  },
  {
    skill: 'Python',
    demand: 90,
    supply: 75,
    averageSalary: 100000,
    growth: 18.7,
    difficulty: 5,
    timeToLearn: 6,
    category: 'Programming',
  },
];

describe('MarketInsightsChart', () => {
  describe('Component Rendering', () => {
    it('should render with default props', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      expect(screen.getByText('Market Insights')).toBeInTheDocument();
      expect(screen.getByText('Skill demand, supply, and market trends')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    });

    it('should render with custom title and description', () => {
      const customTitle = 'Custom Market Analysis';
      const customDescription = 'Custom market description';
      
      render(
        <MarketInsightsChart
          data={mockMarketData}
          title={customTitle}
          description={customDescription}
        />
      );
      
      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.getByText(customDescription)).toBeInTheDocument();
    });

    it('should render without description when not provided', () => {
      render(
        <MarketInsightsChart
          data={mockMarketData}
          title="Test Title"
          description=""
        />
      );
      
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.queryByText('Skill demand, supply')).not.toBeInTheDocument();
    });
  });

  describe('Chart Components', () => {
    it('should render all required chart components', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
      expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument();
      expect(screen.getByTestId('x-axis')).toBeInTheDocument();
      expect(screen.getByTestId('y-axis')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    });

    it('should render bars for demand and supply by default', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      const bars = screen.getAllByTestId('bar');
      expect(bars).toHaveLength(2);
      
      expect(bars[0]).toHaveAttribute('data-key', 'demand');
      expect(bars[0]).toHaveAttribute('data-fill', '#3b82f6');
      
      expect(bars[1]).toHaveAttribute('data-key', 'supply');
      expect(bars[1]).toHaveAttribute('data-fill', '#10b981');
    });

    it('should set correct dataKey for x-axis', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      const xAxis = screen.getByTestId('x-axis');
      expect(xAxis).toHaveAttribute('data-key', 'skill');
    });

    it('should render legend when showLegend is true', () => {
      render(<MarketInsightsChart data={mockMarketData} showLegend={true} />);
      expect(screen.getByTestId('legend')).toBeInTheDocument();
    });

    it('should not render legend when showLegend is false', () => {
      render(<MarketInsightsChart data={mockMarketData} showLegend={false} />);
      expect(screen.queryByTestId('legend')).not.toBeInTheDocument();
    });
  });

  describe('Metric Selection', () => {
    it('should display demand and supply metrics by default', () => {
      render(<MarketInsightsChart data={mockMarketData} />);

      expect(screen.getAllByText('Demand vs Supply')).toHaveLength(2); // Button and chart title
    });

    it('should allow switching to salary metrics', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="salary" />);
      
      expect(screen.getByText('Average Salary')).toBeInTheDocument();
    });

    it('should allow switching to growth metrics', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="growth" />);
      
      expect(screen.getByText('Growth Rate')).toBeInTheDocument();
    });

    it('should allow switching to difficulty metrics', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="difficulty" />);
      
      expect(screen.getByText('Learning Difficulty')).toBeInTheDocument();
    });
  });

  describe('Market Summary Cards', () => {
    it('should display market summary statistics', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      // Average demand: (85 + 80 + 90) / 3 = 85.0
      expect(screen.getByText('85.0')).toBeInTheDocument();
      expect(screen.getByText('Avg Demand')).toBeInTheDocument();
      
      // Average supply: (70 + 65 + 75) / 3 = 70.0
      expect(screen.getByText('70.0')).toBeInTheDocument();
      expect(screen.getByText('Avg Supply')).toBeInTheDocument();
      
      // Market gap: 85.0 - 70.0 = 15.0
      expect(screen.getByText('15.0')).toBeInTheDocument();
      expect(screen.getByText('Market Gap')).toBeInTheDocument();
    });

    it('should handle empty data gracefully', () => {
      render(<MarketInsightsChart data={[]} />);

      expect(screen.getAllByText('0.0')).toHaveLength(3); // Should show 0 for all three metrics
    });

    it('should calculate salary statistics correctly', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="salary" />);

      // Average salary: (95000 + 90000 + 100000) / 3 = 95000
      expect(screen.getAllByText('$95,000')).toHaveLength(2); // Summary card and recommendation
      expect(screen.getByText('Avg Salary')).toBeInTheDocument();

      // Highest salary: 100000
      expect(screen.getAllByText('$100,000')).toHaveLength(2); // Summary card and recommendation
      expect(screen.getByText('Highest')).toBeInTheDocument();

      // Lowest salary: 90000
      expect(screen.getAllByText('$90,000')).toHaveLength(2); // Summary card and recommendation
      expect(screen.getByText('Lowest')).toBeInTheDocument();
    });

    it('should calculate growth statistics correctly', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="growth" />);
      
      // Average growth: (12.5 + 15.2 + 18.7) / 3 = 15.5%
      expect(screen.getByText('15.5%')).toBeInTheDocument();
      expect(screen.getByText('Avg Growth')).toBeInTheDocument();
      
      // Highest growth: 18.7%
      expect(screen.getByText('18.7%')).toBeInTheDocument();
      expect(screen.getByText('Highest')).toBeInTheDocument();
      
      // Lowest growth: 12.5%
      expect(screen.getByText('12.5%')).toBeInTheDocument();
      expect(screen.getByText('Lowest')).toBeInTheDocument();
    });
  });

  describe('Skill Recommendations', () => {
    it('should display high-demand skills', () => {
      render(<MarketInsightsChart data={mockMarketData} />);

      expect(screen.getByText('High Demand Skills')).toBeInTheDocument();
      // Python has highest demand (90) - appears in both high demand and market opportunities
      expect(screen.getAllByText('Python')).toHaveLength(2);
    });

    it('should display skills with market gaps', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      expect(screen.getByText('Market Opportunities')).toBeInTheDocument();
      // Skills with demand > supply should be listed
    });

    it('should display fastest growing skills', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="growth" />);
      
      expect(screen.getByText('Fastest Growing')).toBeInTheDocument();
      // Python has highest growth (18.7%)
      expect(screen.getByText('Python')).toBeInTheDocument();
    });

    it('should display highest paying skills', () => {
      render(<MarketInsightsChart data={mockMarketData} metric="salary" />);
      
      expect(screen.getByText('Highest Paying')).toBeInTheDocument();
      // Python has highest salary (100000)
      expect(screen.getByText('Python')).toBeInTheDocument();
    });
  });

  describe('Data Handling', () => {
    it('should handle skills with zero values', () => {
      const dataWithZeros = [
        {
          skill: 'New Technology',
          demand: 0,
          supply: 0,
          averageSalary: 0,
          growth: 0,
          difficulty: 0,
          timeToLearn: 0,
          category: 'Emerging',
        },
      ];

      render(<MarketInsightsChart data={dataWithZeros} />);

      expect(screen.getByText('New Technology')).toBeInTheDocument();
      expect(screen.getAllByText('0.0')).toHaveLength(3); // Three summary cards
    });

    it('should sort skills by selected metric', () => {
      render(<MarketInsightsChart data={mockMarketData} sortBy="demand" />);

      // Should be sorted by demand: Python (90), JavaScript (85), React (80)
      expect(screen.getAllByText('Python')).toHaveLength(2); // High demand and market opportunities
    });

    it('should filter skills by category when provided', () => {
      render(<MarketInsightsChart data={mockMarketData} filterCategory="Programming" />);

      expect(screen.getAllByText('JavaScript')).toHaveLength(2); // High demand and market opportunities
      expect(screen.getAllByText('Python')).toHaveLength(2); // High demand and market opportunities
      expect(screen.queryAllByText('React')).toHaveLength(0); // Should not appear when filtered
    });
  });

  describe('Customization', () => {
    it('should accept custom height', () => {
      const customHeight = 500;
      render(<MarketInsightsChart data={mockMarketData} height={customHeight} />);
      
      const container = screen.getByTestId('responsive-container').parentElement;
      expect(container).toHaveStyle(`height: ${customHeight}px`);
    });

    it('should accept custom colors', () => {
      const customColors = {
        demand: '#ff0000',
        supply: '#00ff00',
        salary: '#0000ff',
        growth: '#ffff00',
      };
      
      render(<MarketInsightsChart data={mockMarketData} colors={customColors} />);
      
      const bars = screen.getAllByTestId('bar');
      expect(bars[0]).toHaveAttribute('data-fill', '#ff0000');
      expect(bars[1]).toHaveAttribute('data-fill', '#00ff00');
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      const title = screen.getByText('Market Insights');
      expect(title.tagName).toBe('DIV'); // CardTitle renders as div
    });

    it('should have descriptive text for screen readers', () => {
      render(<MarketInsightsChart data={mockMarketData} />);
      
      expect(screen.getByText('Skill demand, supply, and market trends')).toBeInTheDocument();
    });
  });
});
