import { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';
import { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';
import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';
import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';

describe('EdgeCaseHandler', () => {
  let edgeCaseHandler: EdgeCaseHandler;
  let mockAssessmentEngine: jest.Mocked<SkillAssessmentEngine>;
  let mockMarketDataService: jest.Mocked<SkillMarketDataService>;
  let mockLearningPathService: jest.Mocked<PersonalizedLearningPathService>;

  beforeEach(() => {
    mockAssessmentEngine = {
      createAssessment: jest.fn(),
      generateQuestions: jest.fn(),
      submitResponse: jest.fn(),
      calculateResults: jest.fn(),
      getAssessment: jest.fn(),
      getAssessmentsByUser: jest.fn(),
    } as any;

    mockMarketDataService = {
      getSkillMarketData: jest.fn(),
      getMultipleSkillsMarketData: jest.fn(),
      analyzeMarketTrends: jest.fn(),
      getSalaryInsights: jest.fn(),
      getLocationBasedMarketData: jest.fn(),
      getMarketBasedRecommendations: jest.fn(),
    } as any;

    mockLearningPathService = {
      generateLearningPath: jest.fn(),
      updateProgress: jest.fn(),
      completeMilestone: jest.fn(),
    } as any;

    edgeCaseHandler = new EdgeCaseHandler(
      mockAssessmentEngine,
      mockMarketDataService,
      mockLearningPathService
    );
  });

  describe('Input Validation Edge Cases', () => {
    describe('Null and Undefined Inputs', () => {
      it('should handle null skill assessment request', async () => {
        const result = await edgeCaseHandler.handleSkillAssessment(null as any);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid input');
        expect(result.errorType).toBe('VALIDATION_ERROR');
        expect(result.fallbackData).toBeDefined();
      });

      it('should handle undefined learning path request', async () => {
        const result = await edgeCaseHandler.handleLearningPathGeneration(undefined as any);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid input');
        expect(result.errorType).toBe('VALIDATION_ERROR');
        expect(result.fallbackData).toBeDefined();
      });

      it('should handle null market data request', async () => {
        const result = await edgeCaseHandler.handleMarketDataRequest(null as any);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid input');
        expect(result.errorType).toBe('VALIDATION_ERROR');
        expect(result.fallbackData).toBeDefined();
      });
    });

    describe('Empty and Invalid Data Types', () => {
      it('should handle empty string inputs', async () => {
        const request = {
          userId: '',
          skillIds: [],
          careerPathId: '',
        };

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Empty required fields');
        expect(result.errorType).toBe('VALIDATION_ERROR');
      });

      it('should handle invalid data types', async () => {
        const request = {
          userId: 123, // Should be string
          skillIds: 'not-an-array', // Should be array
          careerPathId: true, // Should be string
        };

        const result = await edgeCaseHandler.handleSkillAssessment(request as any);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid data types');
        expect(result.errorType).toBe('VALIDATION_ERROR');
      });

      it('should handle extremely large inputs', async () => {
        const largeString = 'a'.repeat(1000000); // 1MB string
        const request = {
          userId: largeString,
          skillIds: Array(10000).fill('skill-id'),
          careerPathId: largeString,
        };

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Values exceed maximum thresholds');
        expect(result.errorType).toBe('VALIDATION_ERROR');
      });
    });

    describe('Security Edge Cases', () => {
      it('should prevent SQL injection attempts', async () => {
        const maliciousRequest = {
          userId: "'; DROP TABLE users; --",
          skillIds: ["'; DELETE FROM skills; --"],
          careerPathId: "1' OR '1'='1",
        };

        const result = await edgeCaseHandler.handleSkillAssessment(maliciousRequest);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Potentially malicious input detected');
        expect(result.errorType).toBe('SECURITY_ERROR');
        expect(result.securityAlert).toBe(true);
      });

      it('should prevent XSS attempts', async () => {
        const xssRequest = {
          userId: '<script>alert("xss")</script>',
          skillIds: ['<img src=x onerror=alert(1)>'],
          careerPathId: 'javascript:alert(1)',
        };

        const result = await edgeCaseHandler.handleSkillAssessment(xssRequest);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Potentially malicious input detected');
        expect(result.errorType).toBe('SECURITY_ERROR');
        expect(result.securityAlert).toBe(true);
      });

      it('should handle malformed JSON gracefully', async () => {
        const malformedJson = '{"userId": "test", "skillIds": [}';

        const result = await edgeCaseHandler.parseAndValidateInput(malformedJson);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid JSON format');
        expect(result.errorType).toBe('PARSING_ERROR');
      });
    });

    describe('Boundary Value Testing', () => {
      it('should handle minimum boundary values', async () => {
        const request = {
          userId: 'user-123', // Valid userId
          skillIds: ['javascript'], // Valid skillIds
          careerPathId: 'path-456',
          timeframe: 0, // Minimum time
          budget: 0, // Minimum budget
        };

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Values below minimum thresholds');
        expect(result.errorType).toBe('VALIDATION_ERROR');
      });

      it('should handle maximum boundary values', async () => {
        const request = {
          userId: 'u'.repeat(256), // Exceeds maximum length
          skillIds: Array(1001).fill('skill'), // Exceeds maximum array size
          careerPathId: 'c'.repeat(256),
          timeframe: 1000, // Exceeds maximum time
          budget: 1000001, // Exceeds maximum budget
        };

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Values exceed maximum thresholds');
        expect(result.errorType).toBe('VALIDATION_ERROR');
      });

      it('should handle negative values', async () => {
        const request = {
          userId: 'user-123',
          skillIds: ['javascript'],
          careerPathId: 'path-456',
          timeframe: -5, // Negative time
          budget: -100, // Negative budget
          availability: -10, // Negative availability
        };

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Negative values not allowed');
        expect(result.errorType).toBe('VALIDATION_ERROR');
      });
    });

    describe('Unicode and Special Characters', () => {
      it('should handle Unicode characters properly', async () => {
        const request = {
          userId: '用户123', // Chinese characters
          skillIds: ['JavaScript™', 'React®'], // Special symbols
          careerPathId: 'Développeur Full-Stack', // French accents
        };

        // Mock successful assessment creation
        mockAssessmentEngine.createAssessment.mockResolvedValue({
          id: 'assessment-123',
          userId: request.userId,
        } as any);

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();
        expect(result.sanitizedInput).toBeDefined();
      });

      it('should handle emoji and special Unicode', async () => {
        const request = {
          userId: 'user🚀123',
          skillIds: ['JavaScript💻', 'React⚛️'],
          careerPathId: 'Full-Stack Developer🌟',
        };

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(true);
        expect(result.sanitizedInput.userId).not.toContain('🚀');
        expect(result.sanitizedInput.skillIds[0]).not.toContain('💻');
      });
    });
  });

  describe('Business Logic Edge Cases', () => {
    describe('Non-existent Data', () => {
      it('should handle non-existent skills gracefully', async () => {
        const request = {
          userId: 'user-123',
          skillIds: ['non-existent-skill', 'another-fake-skill'],
          careerPathId: 'path-456',
        };

        mockAssessmentEngine.createAssessment.mockImplementation(() => {
          throw new Error('Skill not found');
        });

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('One or more skills not found');
        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
        expect(result.fallbackData).toBeDefined();
        expect(result.suggestedAlternatives).toBeDefined();
      });

      it('should handle non-existent career paths', async () => {
        const request = {
          userId: 'user-123',
          currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
          targetRole: 'Quantum Developer', // Non-existent role
          timeframe: 6,
          learningStyle: 'structured' as const,
          availability: 10,
          budget: 500,
        };

        mockLearningPathService.generateLearningPath.mockImplementation(() => {
          throw new Error('Career path not found');
        });

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Career path not found');
        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
        expect(result.suggestedAlternatives).toBeDefined();
      });

      it('should handle users with no existing data', async () => {
        const request = {
          userId: 'new-user-with-no-data',
          skillIds: ['javascript'],
          careerPathId: 'path-456',
        };

        mockAssessmentEngine.getAssessmentsByUser.mockReturnValue([]);
        mockAssessmentEngine.createAssessment.mockResolvedValue({
          id: 'new-user-assessment',
          userId: request.userId,
        } as any);

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();
        expect(result.isNewUser).toBe(true);
        expect(result.onboardingRecommendations).toBeDefined();
      });
    });

    describe('Conflicting Requirements', () => {
      it('should handle circular dependencies in learning paths', async () => {
        const request = {
          userId: 'user-123',
          currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
          targetRole: 'Full Stack Developer',
          timeframe: 6,
          learningStyle: 'structured' as const,
          availability: 10,
          budget: 500,
        };

        mockLearningPathService.generateLearningPath.mockImplementation(() => {
          throw new Error('Circular dependency detected');
        });

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Circular dependency');
        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
        expect(result.fallbackData).toBeDefined();
      });

      it('should handle impossible time/budget constraints', async () => {
        const request = {
          userId: 'user-123',
          currentSkills: [{ skill: 'html', level: 1, confidence: 2 }],
          targetRole: 'Senior Full Stack Developer',
          timeframe: 1, // Impossible timeframe
          learningStyle: 'casual' as const,
          availability: 1, // Very low availability
          budget: 1, // Impossible budget
        };

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Impossible constraints');
        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
        expect(result.feasibilityAnalysis).toBeDefined();
        expect(result.suggestedAdjustments).toBeDefined();
      });
    });

    describe('Data Consistency Issues', () => {
      it('should handle inconsistent skill ratings', async () => {
        const request = {
          userId: 'user-123',
          skillAssessments: [
            { skill: 'javascript', selfRating: 9, confidenceLevel: 2 }, // Inconsistent
            { skill: 'react', selfRating: 3, confidenceLevel: 9 }, // Inconsistent
          ],
        };

        const result = await edgeCaseHandler.validateSkillConsistency(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Inconsistent skill ratings');
        expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
        expect(result.inconsistencies).toBeDefined();
        expect(result.suggestedCorrections).toBeDefined();
      });

      it('should handle conflicting market data', async () => {
        const skill = 'javascript';

        mockMarketDataService.getSkillMarketData.mockResolvedValue({
          skill,
          demand: 95,
          supply: 98, // Supply higher than demand (unusual)
          averageSalary: -50000, // Negative salary (impossible)
          growth: 150, // Growth > 100% (suspicious)
          difficulty: 15, // Difficulty > 10 (out of range)
          timeToLearn: -5, // Negative time (impossible)
          category: '',
          lastUpdated: new Date(),
        });

        const result = await edgeCaseHandler.handleMarketDataRequest({ skill });

        expect(result.success).toBe(false);
        expect(result.error).toContain('Inconsistent market data');
        expect(result.errorType).toBe('DATA_CONSISTENCY_ERROR');
        expect(result.correctedData).toBeDefined();
      });
    });
  });

  describe('System Failure Edge Cases', () => {
    describe('Database Failures', () => {
      it('should handle database connection failures', async () => {
        const request = {
          userId: 'user-123',
          skillIds: ['javascript'],
          careerPathId: 'path-456',
        };

        mockAssessmentEngine.createAssessment.mockImplementation(() => {
          throw new Error('Database connection failed');
        });

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Database connection failed');
        expect(result.errorType).toBe('SYSTEM_ERROR');
        expect(result.retryable).toBe(true);
        expect(result.fallbackData).toBeDefined();
      });

      it('should handle database timeout errors', async () => {
        const request = {
          userId: 'user-123',
          skillIds: ['javascript'],
          careerPathId: 'path-456',
        };

        mockAssessmentEngine.createAssessment.mockImplementation(() => {
          throw new Error('Query timeout');
        });

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Query timeout');
        expect(result.errorType).toBe('TIMEOUT_ERROR');
        expect(result.retryable).toBe(true);
      });
    });

    describe('AI Service Failures', () => {
      it('should handle AI service timeouts', async () => {
        const request = {
          userId: 'user-123',
          currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
          targetRole: 'Full Stack Developer',
          timeframe: 6,
          learningStyle: 'structured' as const,
          availability: 10,
          budget: 500,
        };

        mockLearningPathService.generateLearningPath.mockImplementation(() => {
          throw new Error('AI service timeout');
        });

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('AI service timeout');
        expect(result.errorType).toBe('TIMEOUT_ERROR');
        expect(result.retryable).toBe(true);
        expect(result.fallbackData).toBeDefined();
      });

      it('should handle AI service rate limiting', async () => {
        const request = {
          userId: 'user-123',
          currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
          targetRole: 'Full Stack Developer',
          timeframe: 6,
          learningStyle: 'structured' as const,
          availability: 10,
          budget: 500,
        };

        mockLearningPathService.generateLearningPath.mockImplementation(() => {
          throw new Error('Rate limit exceeded');
        });

        const result = await edgeCaseHandler.handleLearningPathGeneration(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Rate limit exceeded');
        expect(result.errorType).toBe('AI_SERVICE_ERROR');
        expect(result.retryAfter).toBeDefined();
        expect(result.fallbackData).toBeDefined();
      });
    });

    describe('Memory and Performance Limits', () => {
      it('should handle memory exhaustion gracefully', async () => {
        const request = {
          userId: 'user-123',
          skillIds: Array(100000).fill('skill'), // Extremely large array
          careerPathId: 'path-456',
        };

        const result = await edgeCaseHandler.handleSkillAssessment(request);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Values exceed maximum thresholds');
        expect(result.errorType).toBe('VALIDATION_ERROR');
        expect(result.suggestedOptimizations).toBeDefined();
      });

      it('should handle processing timeouts', async () => {
        const request = {
          userId: 'user-123',
          currentSkills: Array(1000).fill({ skill: 'javascript', level: 5, confidence: 6 }),
          targetRole: 'Full Stack Developer',
          timeframe: 6,
          learningStyle: 'structured' as const,
          availability: 10,
          budget: 500,
        };

        // Mock a long-running operation
        mockLearningPathService.generateLearningPath.mockImplementation(() => {
          return new Promise((resolve) => {
            setTimeout(() => resolve({} as any), 60000); // 60 second delay
          });
        });

        const result = await edgeCaseHandler.handleLearningPathGeneration(request, { timeout: 5000 });

        expect(result.success).toBe(false);
        expect(result.error).toContain('Processing timeout');
        expect(result.errorType).toBe('TIMEOUT_ERROR');
        expect(result.partialResults).toBeDefined();
      });
    });
  });

  describe('Concurrency Edge Cases', () => {
    it('should handle concurrent assessment submissions', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      // Simulate concurrent requests
      const promises = Array(10).fill(null).map(() => 
        edgeCaseHandler.handleSkillAssessment(request)
      );

      const results = await Promise.all(promises);

      // Should handle concurrency gracefully
      expect(results.every(r => r.success || r.errorType === 'CONCURRENCY_ERROR')).toBe(true);
      expect(results.filter(r => r.success).length).toBeGreaterThan(0);
    });

    it('should handle resource contention', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Resource locked');
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Resource locked');
      expect(result.errorType).toBe('CONCURRENCY_ERROR');
      expect(result.retryable).toBe(true);
    });
  });

  describe('Recovery and Fallback Mechanisms', () => {
    it('should implement automatic retry for transient failures', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      let callCount = 0;
      mockAssessmentEngine.createAssessment.mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          throw new Error('Transient error');
        }
        return { id: 'assessment-123' } as any;
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request, { maxRetries: 3 });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(2);
      expect(callCount).toBe(3);
    });

    it('should provide fallback data when services fail', async () => {
      const request = {
        skill: 'javascript',
      };

      mockMarketDataService.getSkillMarketData.mockImplementation(() => {
        throw new Error('Service unavailable');
      });

      const result = await edgeCaseHandler.handleMarketDataRequest(request);

      expect(result.success).toBe(false);
      expect(result.fallbackData).toBeDefined();
      expect(result.fallbackData.skill).toBe('javascript');
      expect(result.fallbackData.isStale).toBe(true);
      expect(result.fallbackData.source).toBe('cache_or_default');
    });

    it('should implement circuit breaker pattern', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      // Simulate multiple failures to trigger circuit breaker
      mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Service down');
      });

      // Make multiple requests to trigger circuit breaker
      for (let i = 0; i < 5; i++) {
        await edgeCaseHandler.handleSkillAssessment(request);
      }

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('CIRCUIT_BREAKER_OPEN');
      expect(result.fallbackData).toBeDefined();
    });
  });

  describe('Monitoring and Alerting', () => {
    it('should log security incidents', async () => {
      const maliciousRequest = {
        userId: "'; DROP TABLE users; --",
        skillIds: ["'; DELETE FROM skills; --"],
        careerPathId: "1' OR '1'='1",
      };

      const logSpy = jest.spyOn(console, 'error').mockImplementation();

      await edgeCaseHandler.handleSkillAssessment(maliciousRequest);

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('SECURITY_ALERT'),
        expect.objectContaining({
          type: 'SQL_INJECTION_ATTEMPT',
          userId: maliciousRequest.userId,
          timestamp: expect.any(Date),
        })
      );

      logSpy.mockRestore();
    });

    it('should track error patterns', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      // Generate multiple errors
      for (let i = 0; i < 5; i++) {
        await edgeCaseHandler.handleSkillAssessment(request);
      }

      const errorStats = await edgeCaseHandler.getErrorStatistics();

      expect(errorStats.totalErrors).toBeGreaterThanOrEqual(5);
      expect(errorStats.errorsByType.SYSTEM_ERROR).toBeGreaterThanOrEqual(5);
      expect(errorStats.mostCommonError).toBe('Database connection failed');
    });
  });
});
